# 🌾 AgroTech Dashboard

Dashboard SaaS para análise de commodities agrícolas, fornecendo preços atualizados de boi gordo, soja e milho em uma interface enxuta e intuitiva.

## 📋 Funcionalidades

- **Preços Atuais**: Visualização dos valores mais recentes de arroba do boi, saca de soja e saca de milho
- **Gráficos Históricos**: Séries temporais dos últimos 30 dias para cada commodity
- **Coleta Automatizada**: Dados atualizados diariamente às 07:00 de fontes confiáveis
- **Interface Responsiva**: Dashboard otimizado para desktop e mobile
- **Cache Local**: Armazenamento em SQLite para acesso rápido aos dados

## 🏗️ Arquitetura

### Backend
- **Node.js + Express**: Servidor HTTP leve e eficiente
- **SQLite**: Banco de dados local para cache de dados
- **Coleta Automatizada**: Scripts agendados para buscar dados das fontes

### Frontend
- **HTML5 + CSS3 + JavaScript**: Interface enxuta sem frameworks pesados
- **Chart.js**: Biblioteca para gráficos de séries temporais
- **Design Responsivo**: Compatível com diferentes dispositivos

### Fontes de Dados
- **CEPEA/ESALQ-USP**: Preços da arroba do boi gordo
- **Notícias Agrícolas**: Cotações de soja e milho
- **IEA-SP**: Dados complementares de commodities

## 🚀 Instalação e Configuração

### Pré-requisitos
- Node.js 16+ 
- npm ou yarn

### 1. Instalar Dependências
```bash
npm install
```

### 2. Inicializar Banco de Dados
```bash
npm run init-db
```

### 3. Popular Dados Históricos (Desenvolvimento)
```bash
npm run collect -- --populate
```

### 4. Iniciar Servidor
```bash
# Desenvolvimento
npm run dev

# Produção
npm start
```

O dashboard estará disponível em: `http://localhost:3000`

## 📊 API Endpoints

### Dashboard
- `GET /api/dashboard` - Valores atuais de todas as commodities
- `GET /api/dashboard/resumo` - Versão simplificada do dashboard
- `GET /api/dashboard/status` - Status da aplicação e última atualização

### Boi Gordo
- `GET /api/boi` - Histórico de 30 dias
- `GET /api/boi/atual` - Preço mais recente
- `GET /api/boi/periodo?dias=X` - Histórico personalizado

### Grãos
- `GET /api/graos?tipo=soja` - Histórico da soja (30 dias)
- `GET /api/graos?tipo=milho` - Histórico do milho (30 dias)
- `GET /api/graos/atual?tipo=soja` - Preço atual da soja
- `GET /api/graos/periodo?tipo=soja&dias=X` - Histórico personalizado

## 🔄 Coleta de Dados

### Execução Manual
```bash
# Coleta completa
npm run collect

# Popular dados históricos
npm run collect -- --populate
```

### Agendamento Automático
A coleta é executada automaticamente todos os dias às 07:00 (horário de Brasília).

### Fontes de Dados
- **Boi**: CEPEA (Centro de Estudos Avançados em Economia Aplicada)
- **Soja/Milho**: Notícias Agrícolas e Agrolink

## 🗄️ Estrutura do Banco de Dados

### Tabela: preco_boi
```sql
CREATE TABLE preco_boi (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data DATE UNIQUE NOT NULL,
    valor REAL NOT NULL,
    fonte TEXT DEFAULT 'CEPEA',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Tabela: preco_grao
```sql
CREATE TABLE preco_grao (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data DATE NOT NULL,
    tipo TEXT NOT NULL,
    valor REAL NOT NULL,
    fonte TEXT DEFAULT 'Notícias Agrícolas',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(data, tipo)
);
```

## 📁 Estrutura do Projeto

```
agrotech/
├── server.js              # Servidor principal
├── package.json           # Dependências e scripts
├── database/
│   ├── init.js            # Inicialização do banco
│   └── agrotech.db        # Banco SQLite (criado automaticamente)
├── routes/
│   ├── boi.js             # Rotas do boi
│   ├── graos.js           # Rotas dos grãos
│   └── dashboard.js       # Rotas do dashboard
├── collectors/
│   └── daily-collector.js # Sistema de coleta
├── public/
│   ├── index.html         # Frontend principal
│   ├── styles.css         # Estilos CSS
│   └── app.js             # JavaScript do frontend
├── config/
│   └── app.js             # Configurações da aplicação
├── utils/
│   ├── logger.js          # Sistema de logging
│   └── helpers.js         # Funções utilitárias
└── logs/                  # Arquivos de log (criado automaticamente)
```

## 🔧 Configuração

### Variáveis de Ambiente
```bash
# Servidor
PORT=3000
NODE_ENV=development

# Banco de dados
DATABASE_PATH=./database/agrotech.db

# Coleta de dados
USE_MOCK_DATA=true  # Para desenvolvimento

# Logging
LOG_LEVEL=info

# Segurança
SESSION_SECRET=your-secret-key
CORS_ORIGIN=*
```

### Configurações de Produção
Para deploy em produção, configure:
- `NODE_ENV=production`
- `USE_MOCK_DATA=false`
- Configure variáveis de ambiente específicas da plataforma

## 📈 Monitoramento

### Logs
- `logs/combined.log` - Todos os logs
- `logs/error.log` - Apenas erros
- `logs/collector.log` - Logs da coleta de dados

### Health Check
- `GET /health` - Status da aplicação

## 🚀 Deploy

### Heroku
```bash
# Instalar Heroku CLI
npm install -g heroku

# Login e criar app
heroku login
heroku create agrotech-dashboard

# Configurar variáveis
heroku config:set NODE_ENV=production
heroku config:set USE_MOCK_DATA=false

# Deploy
git push heroku main
```

### Render/Railway
1. Conecte o repositório
2. Configure as variáveis de ambiente
3. Deploy automático

## 🔮 Extensões Futuras

- **Alertas**: Notificações por email/WhatsApp quando preços ultrapassarem limites
- **Autenticação**: Sistema de usuários e planos pagos
- **Mais Commodities**: Café, algodão, açúcar, etanol
- **Contratos Futuros**: Integração com B3 e CME
- **Relatórios**: Exportação em PDF/Excel
- **API Pública**: Endpoints para terceiros

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Email: <EMAIL>

---

**AgroTech Dashboard** - Simplificando a análise de commodities agrícolas 🌾
