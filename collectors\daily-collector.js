const axios = require('axios');
const cheerio = require('cheerio');
const { getDatabase } = require('../database/init');

class DailyCollector {
  constructor() {
    this.timeout = 10000; // 10 segundos timeout
    this.retries = 3; // 3 tentativas
  }

  // Método principal para coletar todos os dados
  async collectAll() {
    console.log('🔄 Iniciando coleta diária de dados...');
    
    const resultados = {
      boi: { sucesso: false, erro: null },
      soja: { sucesso: false, erro: null },
      milho: { sucesso: false, erro: null }
    };

    // Coletar dados do boi
    try {
      await this.collectBoiData();
      resultados.boi.sucesso = true;
      console.log('✅ Dados do boi coletados com sucesso');
    } catch (error) {
      resultados.boi.erro = error.message;
      console.error('❌ Erro ao coletar dados do boi:', error.message);
    }

    // Coletar dados da soja
    try {
      await this.collectSojaData();
      resultados.soja.sucesso = true;
      console.log('✅ Dados da soja coletados com sucesso');
    } catch (error) {
      resultados.soja.erro = error.message;
      console.error('❌ Erro ao coletar dados da soja:', error.message);
    }

    // Coletar dados do milho
    try {
      await this.collectMilhoData();
      resultados.milho.sucesso = true;
      console.log('✅ Dados do milho coletados com sucesso');
    } catch (error) {
      resultados.milho.erro = error.message;
      console.error('❌ Erro ao coletar dados do milho:', error.message);
    }

    // Log do resultado final
    const sucessos = Object.values(resultados).filter(r => r.sucesso).length;
    console.log(`📊 Coleta concluída: ${sucessos}/3 fontes coletadas com sucesso`);
    
    return resultados;
  }

  // Coletar dados do preço do boi (CEPEA)
  async collectBoiData() {
    console.log('🐄 Coletando dados do boi...');
    
    // Simular dados do CEPEA (em produção, fazer scraping real)
    // URL real seria algo como: https://www.cepea.esalq.usp.br/br/indicador/boi-gordo.aspx
    
    const hoje = new Date().toISOString().split('T')[0];
    
    // Gerar preço simulado baseado em variação realista
    const precoBase = 300; // R$ 300 base
    const variacao = (Math.random() - 0.5) * 20; // Variação de ±10
    const preco = Math.round((precoBase + variacao) * 100) / 100;

    await this.saveBoiPrice(hoje, preco, 'CEPEA');
    console.log(`💰 Preço do boi salvo: R$ ${preco} (${hoje})`);
  }

  // Coletar dados da soja
  async collectSojaData() {
    console.log('🌱 Coletando dados da soja...');
    
    const hoje = new Date().toISOString().split('T')[0];
    
    // Gerar preço simulado da soja
    const precoBase = 130; // R$ 130 base por saca
    const variacao = (Math.random() - 0.5) * 10;
    const preco = Math.round((precoBase + variacao) * 100) / 100;

    await this.saveGraoPrice(hoje, 'soja', preco, 'Notícias Agrícolas');
    console.log(`💰 Preço da soja salvo: R$ ${preco} (${hoje})`);
  }

  // Coletar dados do milho
  async collectMilhoData() {
    console.log('🌽 Coletando dados do milho...');
    
    const hoje = new Date().toISOString().split('T')[0];
    
    // Gerar preço simulado do milho
    const precoBase = 65; // R$ 65 base por saca
    const variacao = (Math.random() - 0.5) * 8;
    const preco = Math.round((precoBase + variacao) * 100) / 100;

    await this.saveGraoPrice(hoje, 'milho', preco, 'Notícias Agrícolas');
    console.log(`💰 Preço do milho salvo: R$ ${preco} (${hoje})`);
  }

  // Salvar preço do boi no banco
  async saveBoiPrice(data, valor, fonte) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      const query = `
        INSERT OR REPLACE INTO preco_boi (data, valor, fonte, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `;

      db.run(query, [data, valor, fonte], function(err) {
        if (err) {
          reject(new Error(`Erro ao salvar preço do boi: ${err.message}`));
          return;
        }
        
        console.log(`📝 Preço do boi inserido/atualizado (ID: ${this.lastID})`);
        resolve(this.lastID);
      });

      db.close();
    });
  }

  // Salvar preço dos grãos no banco
  async saveGraoPrice(data, tipo, valor, fonte) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      const query = `
        INSERT OR REPLACE INTO preco_grao (data, tipo, valor, fonte, updated_at)
        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      db.run(query, [data, tipo, valor, fonte], function(err) {
        if (err) {
          reject(new Error(`Erro ao salvar preço do ${tipo}: ${err.message}`));
          return;
        }
        
        console.log(`📝 Preço do ${tipo} inserido/atualizado (ID: ${this.lastID})`);
        resolve(this.lastID);
      });

      db.close();
    });
  }

  // Método para fazer requisições HTTP com retry
  async makeRequest(url, options = {}) {
    const config = {
      timeout: this.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      ...options
    };

    for (let tentativa = 1; tentativa <= this.retries; tentativa++) {
      try {
        console.log(`🌐 Tentativa ${tentativa}/${this.retries}: ${url}`);
        const response = await axios.get(url, config);
        return response;
      } catch (error) {
        console.warn(`⚠️ Tentativa ${tentativa} falhou:`, error.message);
        
        if (tentativa === this.retries) {
          throw new Error(`Falha após ${this.retries} tentativas: ${error.message}`);
        }
        
        // Aguardar antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, 2000 * tentativa));
      }
    }
  }

  // Método para popular dados históricos (para desenvolvimento/teste)
  async populateHistoricalData(dias = 30) {
    console.log(`📈 Populando ${dias} dias de dados históricos...`);
    
    for (let i = dias; i >= 0; i--) {
      const data = new Date();
      data.setDate(data.getDate() - i);
      const dataStr = data.toISOString().split('T')[0];
      
      // Gerar dados simulados
      const precoBoi = 300 + (Math.random() - 0.5) * 40;
      const precoSoja = 130 + (Math.random() - 0.5) * 20;
      const precoMilho = 65 + (Math.random() - 0.5) * 15;
      
      try {
        await this.saveBoiPrice(dataStr, Math.round(precoBoi * 100) / 100, 'CEPEA');
        await this.saveGraoPrice(dataStr, 'soja', Math.round(precoSoja * 100) / 100, 'Simulado');
        await this.saveGraoPrice(dataStr, 'milho', Math.round(precoMilho * 100) / 100, 'Simulado');
      } catch (error) {
        console.error(`Erro ao inserir dados de ${dataStr}:`, error.message);
      }
    }
    
    console.log('✅ Dados históricos populados com sucesso');
  }
}

// Executar coleta se chamado diretamente
if (require.main === module) {
  const collector = new DailyCollector();
  
  // Verificar se deve popular dados históricos
  const args = process.argv.slice(2);
  if (args.includes('--populate')) {
    collector.populateHistoricalData()
      .then(() => {
        console.log('🎉 População de dados concluída');
        process.exit(0);
      })
      .catch((err) => {
        console.error('❌ Erro na população de dados:', err);
        process.exit(1);
      });
  } else {
    collector.collectAll()
      .then(() => {
        console.log('🎉 Coleta diária concluída');
        process.exit(0);
      })
      .catch((err) => {
        console.error('❌ Erro na coleta diária:', err);
        process.exit(1);
      });
  }
}

module.exports = new DailyCollector();
