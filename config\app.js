// Configurações da aplicação AgroTech Dashboard

module.exports = {
  // Configurações do servidor
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development'
  },

  // Configurações do banco de dados
  database: {
    path: process.env.DATABASE_PATH || './database/agrotech.db',
    timeout: 10000, // 10 segundos
    busyTimeout: 30000 // 30 segundos
  },

  // Configurações de coleta de dados
  collector: {
    // Horário da coleta diária (formato cron)
    schedule: '0 7 * * *', // 07:00 todos os dias
    timezone: 'America/Sao_Paulo',
    
    // Configurações de timeout e retry
    timeout: 15000, // 15 segundos
    retries: 3,
    retryDelay: 2000, // 2 segundos
    
    // URLs das fontes de dados
    sources: {
      cepea: {
        url: 'https://www.cepea.esalq.usp.br/br/indicador/boi-gordo.aspx',
        name: 'CEPEA/ESALQ-USP',
        type: 'scraping'
      },
      iea: {
        url: 'http://www.iea.sp.gov.br/out/bancodedados.php',
        name: 'IEA-SP',
        type: 'csv'
      },
      noticiasAgricolas: {
        url: 'https://www.noticiasagricolas.com.br/cotacoes',
        name: 'Notícias Agrícolas',
        type: 'scraping'
      },
      agrolink: {
        url: 'https://www.agrolink.com.br/cotacoes/',
        name: 'Agrolink',
        type: 'scraping'
      }
    }
  },

  // Configurações de cache
  cache: {
    // Tempo de vida do cache em segundos
    ttl: 3600, // 1 hora
    
    // Número máximo de registros históricos a manter
    maxHistoricalRecords: 365, // 1 ano
    
    // Limpeza automática de dados antigos
    cleanupSchedule: '0 2 * * 0' // 02:00 aos domingos
  },

  // Configurações de API
  api: {
    // Limite de requisições por IP (por minuto)
    rateLimit: {
      windowMs: 60000, // 1 minuto
      max: 100 // 100 requisições por minuto
    },
    
    // Configurações de CORS
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      credentials: true
    },
    
    // Configurações de compressão
    compression: {
      threshold: 1024, // Comprimir responses > 1KB
      level: 6 // Nível de compressão (1-9)
    }
  },

  // Configurações de logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: 'combined',
    
    // Arquivos de log
    files: {
      error: './logs/error.log',
      combined: './logs/combined.log',
      collector: './logs/collector.log'
    },
    
    // Rotação de logs
    rotation: {
      maxSize: '10m', // 10MB
      maxFiles: 5
    }
  },

  // Configurações de monitoramento
  monitoring: {
    // Healthcheck endpoint
    healthcheck: {
      enabled: true,
      path: '/health'
    },
    
    // Métricas
    metrics: {
      enabled: process.env.NODE_ENV === 'production',
      path: '/metrics'
    }
  },

  // Configurações de segurança
  security: {
    // Headers de segurança (Helmet.js)
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
          scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"]
        }
      },
      
      // HSTS
      hsts: {
        maxAge: 31536000, // 1 ano
        includeSubDomains: true,
        preload: true
      }
    },
    
    // Configurações de sessão (se necessário no futuro)
    session: {
      secret: process.env.SESSION_SECRET || 'agrotech-dashboard-secret',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 horas
      }
    }
  },

  // Configurações de desenvolvimento
  development: {
    // Dados simulados para desenvolvimento
    useMockData: process.env.USE_MOCK_DATA === 'true',
    
    // Intervalo de coleta mais frequente para testes
    fastCollectionInterval: 60000, // 1 minuto
    
    // Logs mais verbosos
    verboseLogging: true
  },

  // Configurações de produção
  production: {
    // Configurações específicas para produção
    enableCompression: true,
    enableCaching: true,
    enableRateLimit: true,
    
    // Configurações de deploy
    deploy: {
      platform: process.env.DEPLOY_PLATFORM || 'heroku',
      buildCommand: 'npm run build',
      startCommand: 'npm start'
    }
  }
};

// Função para obter configuração baseada no ambiente
function getConfig() {
  const config = module.exports;
  const env = config.server.env;
  
  // Aplicar configurações específicas do ambiente
  if (env === 'development' && config.development) {
    return { ...config, ...config.development };
  } else if (env === 'production' && config.production) {
    return { ...config, ...config.production };
  }
  
  return config;
}

module.exports.getConfig = getConfig;
