const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, 'agrotech.db');

// Função para inicializar o banco de dados
function initDatabase() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Erro ao conectar com o banco de dados:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Conectado ao banco SQLite');
    });

    // Criar tabela para preços do boi
    db.run(`
      CREATE TABLE IF NOT EXISTS preco_boi (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        data DATE UNIQUE NOT NULL,
        valor REAL NOT NULL,
        fonte TEXT DEFAULT 'CEPEA',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `, (err) => {
      if (err) {
        console.error('Erro ao criar tabela preco_boi:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Tabela preco_boi criada/verificada');
    });

    // Criar tabela para preços dos grãos
    db.run(`
      CREATE TABLE IF NOT EXISTS preco_grao (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        data DATE NOT NULL,
        tipo TEXT NOT NULL,
        valor REAL NOT NULL,
        fonte TEXT DEFAULT 'Notícias Agrícolas',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(data, tipo)
      )
    `, (err) => {
      if (err) {
        console.error('Erro ao criar tabela preco_grao:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Tabela preco_grao criada/verificada');
    });

    // Criar índices para melhor performance
    db.run(`CREATE INDEX IF NOT EXISTS idx_preco_boi_data ON preco_boi(data DESC)`);
    db.run(`CREATE INDEX IF NOT EXISTS idx_preco_grao_data_tipo ON preco_grao(data DESC, tipo)`);

    db.close((err) => {
      if (err) {
        console.error('Erro ao fechar conexão:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Banco de dados inicializado com sucesso');
      resolve();
    });
  });
}

// Função para obter conexão com o banco
function getDatabase() {
  return new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
      console.error('Erro ao conectar com o banco:', err.message);
      throw err;
    }
  });
}

// Executar inicialização se chamado diretamente
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('🎉 Inicialização do banco concluída');
      process.exit(0);
    })
    .catch((err) => {
      console.error('❌ Falha na inicialização:', err);
      process.exit(1);
    });
}

module.exports = {
  initDatabase,
  getDatabase,
  DB_PATH
};
