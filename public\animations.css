/* Animações e Micro-interações Avançadas */

/* Animação de entrada suave para cards */
.summary-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.summary-card:nth-child(1) { animation-delay: 0.1s; }
.summary-card:nth-child(2) { animation-delay: 0.2s; }
.summary-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animação para gráficos */
.chart-container {
    animation: fadeInScale 0.8s ease-out;
    animation-fill-mode: both;
}

.chart-container:nth-child(1) { animation-delay: 0.4s; }
.chart-container:nth-child(2) { animation-delay: 0.5s; }
.chart-container:nth-child(3) { animation-delay: 0.6s; }

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover effect melhorado para cards */
.summary-card {
    position: relative;
    overflow: hidden;
}

.summary-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.summary-card:hover::after {
    left: 100%;
}

/* Animação do preço quando atualizado */
.price.updating {
    animation: priceUpdate 0.6s ease-in-out;
}

@keyframes priceUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); color: var(--card-color); }
    100% { transform: scale(1); }
}

/* Loading skeleton para cards */
.card-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Smooth transitions para todos os elementos interativos */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Animação para o status dot */
.status-dot {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Hover effect para botões */
.refresh-btn {
    position: relative;
    overflow: hidden;
}

.refresh-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.refresh-btn:hover::before {
    width: 100px;
    height: 100px;
}

/* Animação para seções */
.section-header {
    animation: fadeInDown 0.6s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Efeito parallax sutil no scroll */
@media (prefers-reduced-motion: no-preference) {
    .summary-section {
        transform: translateY(0);
        transition: transform 0.1s ease-out;
    }
}

/* Animação de erro */
.error-message {
    animation: shakeIn 0.6s ease-out;
}

@keyframes shakeIn {
    0% {
        opacity: 0;
        transform: translateX(-10px);
    }
    25% {
        transform: translateX(10px);
    }
    50% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animação para tooltips dos gráficos */
.chart-tooltip {
    animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade das animações */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (preparação futura) */
@media (prefers-color-scheme: dark) {
    .card-skeleton {
        background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    }
}
