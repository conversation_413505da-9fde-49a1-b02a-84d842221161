// AgroTech Dashboard - Frontend JavaScript
class AgroTechDashboard {
    constructor() {
        this.charts = {};
        this.lastUpdate = null;
        this.refreshInterval = null;

        // Configurações dos gráficos
        this.chartConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return `R$ ${context.parsed.y.toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 8
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'R$ ' + value.toFixed(0);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        };

        this.init();
    }

    // Inicializar dashboard
    async init() {
        console.log('🚀 Inicializando AgroTech Dashboard...');

        // Configurar event listeners
        this.setupEventListeners();

        // Carregar dados iniciais
        await this.loadDashboard();

        // Configurar atualização automática (a cada 5 minutos)
        this.setupAutoRefresh();

        console.log('✅ Dashboard inicializado com sucesso');
    }

    // Configurar event listeners
    setupEventListeners() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadDashboard());
        }

        // Atualizar ao focar na janela
        window.addEventListener('focus', () => {
            if (this.lastUpdate && Date.now() - this.lastUpdate > 300000) { // 5 minutos
                this.loadDashboard();
            }
        });
    }

    // Configurar atualização automática
    setupAutoRefresh() {
        // Atualizar a cada 5 minutos
        this.refreshInterval = setInterval(() => {
            this.loadDashboard(false); // Atualização silenciosa
        }, 300000);
    }

    // Carregar todos os dados do dashboard
    async loadDashboard(showLoading = true) {
        try {
            if (showLoading) {
                this.showLoading();
            }

            // Buscar dados do dashboard
            const dashboardData = await this.fetchDashboardData();

            // Buscar dados históricos
            const historicalData = await this.fetchHistoricalData();

            // Atualizar interface
            this.updateSummaryCards(dashboardData);
            this.updateCharts(historicalData);
            this.updateLastUpdateTime();

            this.showDashboard();
            this.lastUpdate = Date.now();

        } catch (error) {
            console.error('❌ Erro ao carregar dashboard:', error);
            this.showError(error.message);
        }
    }

    // Buscar dados atuais do dashboard
    async fetchDashboardData() {
        const response = await fetch('/api/dashboard');

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Erro ao buscar dados');
        }

        return data.dados;
    }

    // Buscar dados históricos
    async fetchHistoricalData() {
        const [boiResponse, sojaResponse, milhoResponse] = await Promise.all([
            fetch('/api/boi'),
            fetch('/api/graos?tipo=soja'),
            fetch('/api/graos?tipo=milho')
        ]);

        if (!boiResponse.ok || !sojaResponse.ok || !milhoResponse.ok) {
            throw new Error('Erro ao buscar dados históricos');
        }

        const [boiData, sojaData, milhoData] = await Promise.all([
            boiResponse.json(),
            sojaResponse.json(),
            milhoResponse.json()
        ]);

        return {
            boi: boiData.dados || [],
            soja: sojaData.dados || [],
            milho: milhoData.dados || []
        };
    }

    // Atualizar cards de resumo
    updateSummaryCards(data) {
        // Atualizar card do boi
        if (data.boi && !data.boi.error) {
            document.getElementById('boi-price').textContent = `R$ ${data.boi.valor.toFixed(2)}`;
            document.getElementById('boi-date').textContent = this.formatDate(data.boi.data);
            document.getElementById('boi-source').textContent = `Fonte: ${data.boi.fonte}`;
        } else {
            this.setCardError('boi', data.boi?.error || 'Dados não disponíveis');
        }

        // Atualizar card da soja
        if (data.soja && !data.soja.error) {
            document.getElementById('soja-price').textContent = `R$ ${data.soja.valor.toFixed(2)}`;
            document.getElementById('soja-date').textContent = this.formatDate(data.soja.data);
            document.getElementById('soja-source').textContent = `Fonte: ${data.soja.fonte}`;
        } else {
            this.setCardError('soja', data.soja?.error || 'Dados não disponíveis');
        }

        // Atualizar card do milho
        if (data.milho && !data.milho.error) {
            document.getElementById('milho-price').textContent = `R$ ${data.milho.valor.toFixed(2)}`;
            document.getElementById('milho-date').textContent = this.formatDate(data.milho.data);
            document.getElementById('milho-source').textContent = `Fonte: ${data.milho.fonte}`;
        } else {
            this.setCardError('milho', data.milho?.error || 'Dados não disponíveis');
        }
    }

    // Definir erro em um card
    setCardError(tipo, erro) {
        document.getElementById(`${tipo}-price`).textContent = 'N/A';
        document.getElementById(`${tipo}-date`).textContent = erro;
        document.getElementById(`${tipo}-source`).textContent = '';
    }

    // Atualizar gráficos
    updateCharts(data) {
        this.updateChart('boi', data.boi, '#d69e2e');
        this.updateChart('soja', data.soja, '#38a169');
        this.updateChart('milho', data.milho, '#ed8936');
    }

    // Atualizar um gráfico específico
    updateChart(tipo, dados, cor) {
        const ctx = document.getElementById(`${tipo}-chart`);
        if (!ctx) return;

        // Verificar se Chart.js está disponível
        if (typeof Chart === 'undefined') {
            console.error('Chart.js não está disponível');
            return;
        }

        // Destruir gráfico existente se houver
        if (this.charts[tipo]) {
            this.charts[tipo].destroy();
        }

        // Preparar dados para o gráfico
        const labels = dados.map(item => this.formatDateShort(item.data));
        const values = dados.map(item => item.valor);

        try {
            // Criar novo gráfico
            this.charts[tipo] = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        borderColor: cor,
                        backgroundColor: cor + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: cor,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: this.chartConfig
            });
        } catch (error) {
            console.error(`Erro ao criar gráfico ${tipo}:`, error);
        }
    }

    // Formatar data completa
    formatDate(dateString) {
        const date = new Date(dateString + 'T00:00:00');
        return date.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    // Formatar data curta para gráficos
    formatDateShort(dateString) {
        const date = new Date(dateString + 'T00:00:00');
        return date.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit'
        });
    }

    // Atualizar horário da última atualização
    updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        document.getElementById('last-update').textContent = `Atualizado às ${timeString}`;
    }

    // Mostrar estado de carregamento
    showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('error').style.display = 'none';
        document.getElementById('dashboard').style.display = 'none';
    }

    // Mostrar dashboard
    showDashboard() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'none';
        document.getElementById('dashboard').style.display = 'block';

        // Atualizar ícones Feather após mostrar o dashboard
        if (typeof feather !== 'undefined') {
            setTimeout(() => feather.replace(), 100);
        }
    }

    // Mostrar erro
    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('dashboard').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('error-text').textContent = message;
    }

    // Destruir dashboard (cleanup)
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    }
}

// Inicializar dashboard quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    // Inicializar ícones Feather
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Verificar se Chart.js foi carregado
    if (typeof Chart === 'undefined') {
        console.error('Chart.js não foi carregado. Tentando carregar novamente...');

        // Tentar carregar Chart.js dinamicamente
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js';
        script.onload = () => {
            console.log('Chart.js carregado com sucesso');
            window.dashboard = new AgroTechDashboard();
        };
        script.onerror = () => {
            console.error('Falha ao carregar Chart.js');
            document.getElementById('error-text').textContent = 'Erro ao carregar biblioteca de gráficos. Verifique sua conexão com a internet.';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            // Inicializar ícones mesmo com erro
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        };
        document.head.appendChild(script);
    } else {
        window.dashboard = new AgroTechDashboard();
    }
});

// Cleanup ao sair da página
window.addEventListener('beforeunload', () => {
    if (window.dashboard) {
        window.dashboard.destroy();
    }
});
