// Fallback para Chart.js caso o CDN falhe
// Este arquivo contém uma versão simplificada de gráficos usando Canvas API

class SimpleChart {
    constructor(ctx, config) {
        this.ctx = ctx.getContext('2d');
        this.canvas = ctx;
        this.config = config;
        this.data = config.data;
        this.options = config.options || {};
        
        this.draw();
    }

    draw() {
        const canvas = this.canvas;
        const ctx = this.ctx;
        const data = this.data;
        
        // Limpar canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Configurar dimensões
        const padding = 40;
        const chartWidth = canvas.width - (padding * 2);
        const chartHeight = canvas.height - (padding * 2);
        
        if (!data.datasets || !data.datasets[0] || !data.datasets[0].data) {
            this.drawNoData(ctx, canvas);
            return;
        }
        
        const values = data.datasets[0].data;
        const labels = data.labels || [];
        
        if (values.length === 0) {
            this.drawNoData(ctx, canvas);
            return;
        }
        
        // Calcular escalas
        const minValue = Math.min(...values);
        const maxValue = Math.max(...values);
        const valueRange = maxValue - minValue || 1;
        
        // Desenhar eixos
        this.drawAxes(ctx, padding, chartWidth, chartHeight, canvas);
        
        // Desenhar linha
        this.drawLine(ctx, values, labels, minValue, valueRange, padding, chartWidth, chartHeight);
        
        // Desenhar pontos
        this.drawPoints(ctx, values, minValue, valueRange, padding, chartWidth, chartHeight);
        
        // Desenhar labels
        this.drawLabels(ctx, labels, values, minValue, maxValue, padding, chartWidth, chartHeight);
    }
    
    drawNoData(ctx, canvas) {
        ctx.fillStyle = '#666';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Sem dados para exibir', canvas.width / 2, canvas.height / 2);
    }
    
    drawAxes(ctx, padding, chartWidth, chartHeight, canvas) {
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 1;
        
        // Eixo X
        ctx.beginPath();
        ctx.moveTo(padding, canvas.height - padding);
        ctx.lineTo(padding + chartWidth, canvas.height - padding);
        ctx.stroke();
        
        // Eixo Y
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.stroke();
    }
    
    drawLine(ctx, values, labels, minValue, valueRange, padding, chartWidth, chartHeight) {
        const color = this.data.datasets[0].borderColor || '#4299e1';
        
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        values.forEach((value, index) => {
            const x = padding + (index / (values.length - 1)) * chartWidth;
            const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        
        ctx.stroke();
        
        // Área preenchida
        if (this.data.datasets[0].fill) {
            ctx.fillStyle = color + '20';
            ctx.lineTo(padding + chartWidth, padding + chartHeight);
            ctx.lineTo(padding, padding + chartHeight);
            ctx.closePath();
            ctx.fill();
        }
    }
    
    drawPoints(ctx, values, minValue, valueRange, padding, chartWidth, chartHeight) {
        const color = this.data.datasets[0].pointBackgroundColor || this.data.datasets[0].borderColor || '#4299e1';
        
        ctx.fillStyle = color;
        
        values.forEach((value, index) => {
            const x = padding + (index / (values.length - 1)) * chartWidth;
            const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;
            
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    }
    
    drawLabels(ctx, labels, values, minValue, maxValue, padding, chartWidth, chartHeight) {
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        // Labels do eixo X (apenas alguns para não sobrecarregar)
        const maxLabels = 6;
        const labelStep = Math.ceil(labels.length / maxLabels);
        
        labels.forEach((label, index) => {
            if (index % labelStep === 0) {
                const x = padding + (index / (labels.length - 1)) * chartWidth;
                const y = padding + chartHeight + 20;
                ctx.fillText(label, x, y);
            }
        });
        
        // Labels do eixo Y
        ctx.textAlign = 'right';
        const ySteps = 5;
        for (let i = 0; i <= ySteps; i++) {
            const value = minValue + (maxValue - minValue) * (i / ySteps);
            const y = padding + chartHeight - (i / ySteps) * chartHeight;
            ctx.fillText('R$ ' + value.toFixed(0), padding - 10, y + 4);
        }
    }
    
    destroy() {
        // Limpar canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
}

// Polyfill para Chart se não estiver disponível
if (typeof Chart === 'undefined') {
    window.Chart = SimpleChart;
    console.log('Usando fallback simples para gráficos');
}
