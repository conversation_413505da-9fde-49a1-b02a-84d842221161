<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroTech Dashboard - Commodities Agrícolas</title>
    <meta name="description" content="Dashboard para análise de preços de commodities agrícolas: boi, soja e milho">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="animations.css">

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- Fallback para Chart.js -->
    <script src="chart-fallback.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23059669' stroke-width='2'><path d='M12 2L2 7l10 5 10-5-10-5z'/><path d='M2 17l10 5 10-5'/><path d='M2 12l10 5 10-5'/></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                        <path d="M2 17l10 5 10-5"/>
                        <path d="M2 12l10 5 10-5"/>
                    </svg>
                </div>
                <div class="logo-text">
                    <h1>AgroTech</h1>
                    <span class="logo-subtitle">Dashboard</span>
                </div>
            </div>
            <div class="header-info">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span id="last-update" class="last-update">Carregando...</span>
                </div>
                <button id="refresh-btn" class="refresh-btn" title="Atualizar dados">
                    <i data-feather="refresh-cw"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">

            <!-- Loading State -->
            <div id="loading" class="loading">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                </div>
                <p>Carregando dados das commodities...</p>
            </div>

            <!-- Error State -->
            <div id="error" class="error-message" style="display: none;">
                <div class="error-icon">
                    <i data-feather="alert-circle"></i>
                </div>
                <h3>Erro ao carregar dados</h3>
                <p id="error-text">Não foi possível conectar com o servidor.</p>
                <button onclick="loadDashboard()" class="retry-btn">
                    <i data-feather="refresh-cw"></i>
                    Tentar novamente
                </button>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboard" class="dashboard" style="display: none;">

                <!-- Current Values Summary -->
                <section class="summary-section">
                    <div class="section-header">
                        <h2 class="section-title">Preços Atuais</h2>
                        <p class="section-subtitle">Valores mais recentes das commodities</p>
                    </div>
                    <div class="summary-cards">
                        <div class="summary-card boi-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                                    </svg>
                                </div>
                                <div class="card-title">
                                    <h3>Arroba do Boi</h3>
                                    <span class="card-unit">por arroba</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="price" id="boi-price">R$ --</div>
                                <div class="card-meta">
                                    <div class="date" id="boi-date">--</div>
                                    <div class="source" id="boi-source">--</div>
                                </div>
                            </div>
                        </div>

                        <div class="summary-card soja-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M12 1v6m0 6v6"/>
                                        <path d="m15.5 8.5-3 3-3-3"/>
                                        <path d="m15.5 15.5-3-3-3 3"/>
                                    </svg>
                                </div>
                                <div class="card-title">
                                    <h3>Saca de Soja</h3>
                                    <span class="card-unit">saca 60kg</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="price" id="soja-price">R$ --</div>
                                <div class="card-meta">
                                    <div class="date" id="soja-date">--</div>
                                    <div class="source" id="soja-source">--</div>
                                </div>
                            </div>
                        </div>

                        <div class="summary-card milho-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                        <path d="M2 17l10 5 10-5"/>
                                        <path d="M2 12l10 5 10-5"/>
                                    </svg>
                                </div>
                                <div class="card-title">
                                    <h3>Saca de Milho</h3>
                                    <span class="card-unit">saca 60kg</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="price" id="milho-price">R$ --</div>
                                <div class="card-meta">
                                    <div class="date" id="milho-date">--</div>
                                    <div class="source" id="milho-source">--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Charts Section -->
                <section class="charts-section">
                    <div class="section-header">
                        <h2 class="section-title">Análise Histórica</h2>
                        <p class="section-subtitle">Evolução dos preços nos últimos 30 dias</p>
                    </div>

                    <div class="charts-grid">
                        <!-- Boi Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <div class="chart-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3>Arroba do Boi Gordo</h3>
                                        <span class="chart-subtitle">Preço em R$ por arroba</span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="boi-chart"></canvas>
                            </div>
                        </div>

                        <!-- Soja Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <div class="chart-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M12 1v6m0 6v6"/>
                                            <path d="m15.5 8.5-3 3-3-3"/>
                                            <path d="m15.5 15.5-3-3-3 3"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3>Saca de Soja</h3>
                                        <span class="chart-subtitle">Preço em R$ por saca de 60kg</span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="soja-chart"></canvas>
                            </div>
                        </div>

                        <!-- Milho Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <div class="chart-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                            <path d="M2 17l10 5 10-5"/>
                                            <path d="M2 12l10 5 10-5"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3>Saca de Milho</h3>
                                        <span class="chart-subtitle">Preço em R$ por saca de 60kg</span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="milho-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>

            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2025 AgroTech Dashboard</p>
                    <p class="footer-update">Dados atualizados diariamente às 07:00</p>
                </div>
                <div class="footer-sources">
                    <p class="disclaimer">
                        <small>
                            <strong>Fontes:</strong> CEPEA/ESALQ-USP (Boi), Notícias Agrícolas (Grãos)<br>
                            Os preços são indicativos e podem variar conforme região e qualidade.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Inicializar ícones Feather
        feather.replace();
    </script>
    <script src="app.js"></script>
</body>
</html>
