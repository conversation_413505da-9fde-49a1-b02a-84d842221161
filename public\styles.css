/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: #f8fafc;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
}

.logo-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.last-update {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.refresh-btn {
    background: #f3f4f6;
    color: #6b7280;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: #e5e7eb;
    color: #374151;
    transform: rotate(180deg);
}

/* Main Content */
.main {
    padding: 2rem 0 4rem;
    min-height: calc(100vh - 140px);
}

/* Loading State */
.loading {
    text-align: center;
    padding: 4rem 0;
    color: #6b7280;
}

.loading-spinner {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 2rem;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.error-message {
    background: white;
    border: 1px solid #fecaca;
    border-radius: 16px;
    padding: 3rem 2rem;
    text-align: center;
    margin: 2rem auto;
    max-width: 500px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.error-icon {
    width: 60px;
    height: 60px;
    background: #fef2f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #dc2626;
}

.error-message h3 {
    color: #dc2626;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.retry-btn {
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    margin-top: 1.5rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.retry-btn:hover {
    background: #b91c1c;
    transform: translateY(-1px);
}

/* Dashboard */
.dashboard {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    color: #111827;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.section-subtitle {
    color: #6b7280;
    font-size: 1.125rem;
    font-weight: 400;
}

/* Summary Section */
.summary-section {
    margin-bottom: 4rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.summary-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: var(--card-color);
}

.card-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--card-bg);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--card-color);
    flex-shrink: 0;
}

.card-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.card-unit {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.card-content .price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
    line-height: 1;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #f3f4f6;
}

.card-meta .date {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

.card-meta .source {
    font-size: 0.75rem;
    color: #9ca3af;
    font-weight: 500;
}

/* Color themes for cards */
.boi-card {
    --card-color: #d97706;
    --card-color-light: #f59e0b;
    --card-bg: #fef3c7;
}

.soja-card {
    --card-color: #059669;
    --card-color-light: #10b981;
    --card-bg: #d1fae5;
}

.milho-card {
    --card-color: #dc2626;
    --card-color-light: #ef4444;
    --card-bg: #fee2e2;
}

/* Charts Section */
.charts-section {
    margin-bottom: 4rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.chart-header {
    margin-bottom: 2rem;
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chart-icon {
    width: 40px;
    height: 40px;
    background: #f3f4f6;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.chart-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.chart-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.chart-wrapper {
    position: relative;
    height: 320px;
}

.chart-wrapper canvas {
    max-height: 100%;
}

/* Footer */
.footer {
    background: #111827;
    color: #d1d5db;
    padding: 3rem 0 2rem;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
}

.footer-info p {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.footer-update {
    color: #9ca3af;
    font-size: 0.875rem;
}

.footer-sources {
    text-align: right;
}

.disclaimer {
    color: #9ca3af;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        padding: 1rem 24px;
    }

    .logo-text h1 {
        font-size: 1.25rem;
    }

    .header-info {
        gap: 1rem;
    }

    .summary-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .chart-wrapper {
        height: 280px;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-sources {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .header .container {
        padding: 1rem 16px;
    }

    .summary-card {
        padding: 1.5rem;
    }

    .card-content .price {
        font-size: 2rem;
    }

    .chart-container {
        padding: 1.5rem;
    }

    .chart-wrapper {
        height: 250px;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Animation for data updates */
.updating {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.updated {
    animation: highlight 0.5s ease;
}

@keyframes highlight {
    0% { background-color: rgba(66, 153, 225, 0.1); }
    100% { background-color: transparent; }
}
