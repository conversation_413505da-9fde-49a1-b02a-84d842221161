const express = require('express');
const { getDatabase } = require('../database/init');

const router = express.Router();

// GET /api/boi - Retorna histórico de preços da arroba do boi (últimos 30 dias)
router.get('/', (req, res) => {
  const db = getDatabase();
  
  // Query para buscar últimos 30 dias
  const query = `
    SELECT data, valor 
    FROM preco_boi 
    WHERE data >= date('now', '-30 days')
    ORDER BY data ASC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Erro ao buscar preços do boi:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    // Formatar dados para o frontend
    const dados = rows.map(row => ({
      data: row.data,
      valor: parseFloat(row.valor)
    }));

    res.json({
      success: true,
      count: dados.length,
      periodo: '30 dias',
      dados: dados
    });
  });

  db.close();
});

// GET /api/boi/atual - Retorna apenas o preço mais recente
router.get('/atual', (req, res) => {
  const db = getDatabase();
  
  const query = `
    SELECT data, valor, fonte
    FROM preco_boi 
    ORDER BY data DESC 
    LIMIT 1
  `;

  db.get(query, [], (err, row) => {
    if (err) {
      console.error('Erro ao buscar preço atual do boi:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    if (!row) {
      res.status(404).json({
        error: 'Dados não encontrados',
        message: 'Nenhum preço de boi encontrado na base de dados'
      });
      return;
    }

    res.json({
      success: true,
      data: row.data,
      valor: parseFloat(row.valor),
      fonte: row.fonte
    });
  });

  db.close();
});

// GET /api/boi/periodo?dias=X - Retorna histórico personalizado
router.get('/periodo', (req, res) => {
  const dias = parseInt(req.query.dias) || 30;
  
  // Validar parâmetro
  if (dias < 1 || dias > 365) {
    res.status(400).json({
      error: 'Parâmetro inválido',
      message: 'O número de dias deve estar entre 1 e 365'
    });
    return;
  }

  const db = getDatabase();
  
  const query = `
    SELECT data, valor 
    FROM preco_boi 
    WHERE data >= date('now', '-${dias} days')
    ORDER BY data ASC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Erro ao buscar histórico do boi:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    const dados = rows.map(row => ({
      data: row.data,
      valor: parseFloat(row.valor)
    }));

    res.json({
      success: true,
      count: dados.length,
      periodo: `${dias} dias`,
      dados: dados
    });
  });

  db.close();
});

module.exports = router;
