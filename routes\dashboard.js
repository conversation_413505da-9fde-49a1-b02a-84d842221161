const express = require('express');
const { getDatabase } = require('../database/init');

const router = express.Router();

// GET /api/dashboard - Retorna valores mais recentes de todos os indicadores
router.get('/', (req, res) => {
  const db = getDatabase();
  
  // Buscar dados mais recentes em paralelo
  const queries = {
    boi: `
      SELECT data, valor, fonte
      FROM preco_boi 
      ORDER BY data DESC 
      LIMIT 1
    `,
    soja: `
      SELECT data, valor, fonte
      FROM preco_grao 
      WHERE tipo = 'soja'
      ORDER BY data DESC 
      LIMIT 1
    `,
    milho: `
      SELECT data, valor, fonte
      FROM preco_grao 
      WHERE tipo = 'milho'
      ORDER BY data DESC 
      LIMIT 1
    `
  };

  const resultados = {};
  let consultasConcluidas = 0;
  const totalConsultas = Object.keys(queries).length;

  // Função para processar resultado de cada consulta
  function processarResultado(tipo, err, row) {
    if (err) {
      console.error(`Erro ao buscar ${tipo}:`, err.message);
      resultados[tipo] = { error: 'Dados não disponíveis' };
    } else if (!row) {
      resultados[tipo] = { error: 'Nenhum dado encontrado' };
    } else {
      resultados[tipo] = {
        data: row.data,
        valor: parseFloat(row.valor),
        fonte: row.fonte
      };
    }

    consultasConcluidas++;

    // Quando todas as consultas terminarem, enviar resposta
    if (consultasConcluidas === totalConsultas) {
      db.close();
      
      // Verificar se temos pelo menos um resultado válido
      const temDados = Object.values(resultados).some(r => !r.error);
      
      if (!temDados) {
        res.status(404).json({
          error: 'Nenhum dado encontrado',
          message: 'Não há dados disponíveis no momento'
        });
        return;
      }

      res.json({
        success: true,
        timestamp: new Date().toISOString(),
        dados: resultados
      });
    }
  }

  // Executar todas as consultas
  db.get(queries.boi, [], (err, row) => processarResultado('boi', err, row));
  db.get(queries.soja, [], (err, row) => processarResultado('soja', err, row));
  db.get(queries.milho, [], (err, row) => processarResultado('milho', err, row));
});

// GET /api/dashboard/resumo - Versão simplificada para exibição rápida
router.get('/resumo', (req, res) => {
  const db = getDatabase();
  
  // Query unificada para melhor performance
  const query = `
    SELECT 
      'boi' as tipo,
      data,
      valor
    FROM preco_boi 
    WHERE data = (SELECT MAX(data) FROM preco_boi)
    
    UNION ALL
    
    SELECT 
      tipo,
      data,
      valor
    FROM preco_grao 
    WHERE (tipo, data) IN (
      SELECT tipo, MAX(data) 
      FROM preco_grao 
      WHERE tipo IN ('soja', 'milho')
      GROUP BY tipo
    )
    ORDER BY tipo
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Erro ao buscar resumo:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    if (rows.length === 0) {
      res.status(404).json({
        error: 'Nenhum dado encontrado',
        message: 'Não há dados disponíveis no momento'
      });
      return;
    }

    // Organizar dados por tipo
    const resumo = {};
    rows.forEach(row => {
      resumo[row.tipo] = {
        data: row.data,
        valor: parseFloat(row.valor)
      };
    });

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      resumo: resumo
    });

    db.close();
  });
});

// GET /api/dashboard/status - Status da aplicação e última atualização
router.get('/status', (req, res) => {
  const db = getDatabase();
  
  const query = `
    SELECT 
      'boi' as fonte,
      MAX(data) as ultima_atualizacao,
      COUNT(*) as total_registros
    FROM preco_boi
    
    UNION ALL
    
    SELECT 
      'graos' as fonte,
      MAX(data) as ultima_atualizacao,
      COUNT(*) as total_registros
    FROM preco_grao
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Erro ao buscar status:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar status',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    const status = {
      aplicacao: 'AgroTech Dashboard',
      versao: '1.0.0',
      timestamp: new Date().toISOString(),
      banco_dados: {}
    };

    rows.forEach(row => {
      status.banco_dados[row.fonte] = {
        ultima_atualizacao: row.ultima_atualizacao,
        total_registros: row.total_registros
      };
    });

    res.json(status);
    db.close();
  });
});

module.exports = router;
