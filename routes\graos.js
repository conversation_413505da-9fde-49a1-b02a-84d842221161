const express = require('express');
const { getDatabase } = require('../database/init');

const router = express.Router();

// Tipos de grãos suportados
const TIPOS_VALIDOS = ['soja', 'milho'];

// GET /api/graos?tipo=soja|milho - Retorna histórico de preços dos grãos
router.get('/', (req, res) => {
  const tipo = req.query.tipo;
  
  // Validar parâmetro tipo
  if (!tipo || !TIPOS_VALIDOS.includes(tipo.toLowerCase())) {
    res.status(400).json({
      error: 'Parâmetro inválido',
      message: `Tipo deve ser um dos seguintes: ${TIPOS_VALIDOS.join(', ')}`,
      tipos_validos: TIPOS_VALIDOS
    });
    return;
  }

  const db = getDatabase();
  
  // Query para buscar últimos 30 dias do tipo específico
  const query = `
    SELECT data, valor, fonte
    FROM preco_grao 
    WHERE tipo = ? AND data >= date('now', '-30 days')
    ORDER BY data ASC
  `;

  db.all(query, [tipo.toLowerCase()], (err, rows) => {
    if (err) {
      console.error(`Erro ao buscar preços de ${tipo}:`, err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    // Formatar dados para o frontend
    const dados = rows.map(row => ({
      data: row.data,
      valor: parseFloat(row.valor)
    }));

    res.json({
      success: true,
      tipo: tipo.toLowerCase(),
      count: dados.length,
      periodo: '30 dias',
      dados: dados
    });
  });

  db.close();
});

// GET /api/graos/atual?tipo=soja|milho - Retorna apenas o preço mais recente
router.get('/atual', (req, res) => {
  const tipo = req.query.tipo;
  
  if (!tipo || !TIPOS_VALIDOS.includes(tipo.toLowerCase())) {
    res.status(400).json({
      error: 'Parâmetro inválido',
      message: `Tipo deve ser um dos seguintes: ${TIPOS_VALIDOS.join(', ')}`,
      tipos_validos: TIPOS_VALIDOS
    });
    return;
  }

  const db = getDatabase();
  
  const query = `
    SELECT data, valor, fonte
    FROM preco_grao 
    WHERE tipo = ?
    ORDER BY data DESC 
    LIMIT 1
  `;

  db.get(query, [tipo.toLowerCase()], (err, row) => {
    if (err) {
      console.error(`Erro ao buscar preço atual de ${tipo}:`, err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    if (!row) {
      res.status(404).json({
        error: 'Dados não encontrados',
        message: `Nenhum preço de ${tipo} encontrado na base de dados`
      });
      return;
    }

    res.json({
      success: true,
      tipo: tipo.toLowerCase(),
      data: row.data,
      valor: parseFloat(row.valor),
      fonte: row.fonte
    });
  });

  db.close();
});

// GET /api/graos/periodo?tipo=soja&dias=X - Retorna histórico personalizado
router.get('/periodo', (req, res) => {
  const tipo = req.query.tipo;
  const dias = parseInt(req.query.dias) || 30;
  
  // Validar parâmetros
  if (!tipo || !TIPOS_VALIDOS.includes(tipo.toLowerCase())) {
    res.status(400).json({
      error: 'Parâmetro inválido',
      message: `Tipo deve ser um dos seguintes: ${TIPOS_VALIDOS.join(', ')}`,
      tipos_validos: TIPOS_VALIDOS
    });
    return;
  }

  if (dias < 1 || dias > 365) {
    res.status(400).json({
      error: 'Parâmetro inválido',
      message: 'O número de dias deve estar entre 1 e 365'
    });
    return;
  }

  const db = getDatabase();
  
  const query = `
    SELECT data, valor 
    FROM preco_grao 
    WHERE tipo = ? AND data >= date('now', '-${dias} days')
    ORDER BY data ASC
  `;

  db.all(query, [tipo.toLowerCase()], (err, rows) => {
    if (err) {
      console.error(`Erro ao buscar histórico de ${tipo}:`, err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    const dados = rows.map(row => ({
      data: row.data,
      valor: parseFloat(row.valor)
    }));

    res.json({
      success: true,
      tipo: tipo.toLowerCase(),
      count: dados.length,
      periodo: `${dias} dias`,
      dados: dados
    });
  });

  db.close();
});

// GET /api/graos/todos - Retorna dados de todos os grãos
router.get('/todos', (req, res) => {
  const db = getDatabase();
  
  const query = `
    SELECT data, tipo, valor
    FROM preco_grao 
    WHERE data >= date('now', '-30 days')
    ORDER BY data ASC, tipo ASC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Erro ao buscar todos os grãos:', err.message);
      res.status(500).json({ 
        error: 'Erro ao buscar dados',
        message: 'Falha na consulta ao banco de dados'
      });
      return;
    }

    // Organizar dados por tipo
    const dadosOrganizados = {};
    
    rows.forEach(row => {
      if (!dadosOrganizados[row.tipo]) {
        dadosOrganizados[row.tipo] = [];
      }
      dadosOrganizados[row.tipo].push({
        data: row.data,
        valor: parseFloat(row.valor)
      });
    });

    res.json({
      success: true,
      periodo: '30 dias',
      dados: dadosOrganizados
    });
  });

  db.close();
});

module.exports = router;
