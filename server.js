const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const path = require('path');
const cron = require('node-cron');

// Importar rotas
const boiRoutes = require('./routes/boi');
const graosRoutes = require('./routes/graos');
const dashboardRoutes = require('./routes/dashboard');

// Importar coletor de dados
const dailyCollector = require('./collectors/daily-collector');

const app = express();
const PORT = process.env.PORT || 3000;

// Middlewares de segurança e performance
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
app.use(compression());
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos
app.use(express.static(path.join(__dirname, 'public')));

// Rotas da API
app.use('/api/boi', boiRoutes);
app.use('/api/graos', graosRoutes);
app.use('/api/dashboard', dashboardRoutes);

// Rota principal - servir o dashboard
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error('Erro:', err.stack);
  res.status(500).json({ 
    error: 'Erro interno do servidor',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Algo deu errado'
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Rota não encontrada' });
});

// Agendar coleta diária às 07:00 (horário de Brasília)
cron.schedule('0 7 * * *', async () => {
  console.log('Iniciando coleta diária de dados...');
  try {
    await dailyCollector.collectAll();
    console.log('Coleta diária concluída com sucesso');
  } catch (error) {
    console.error('Erro na coleta diária:', error);
  }
}, {
  timezone: "America/Sao_Paulo"
});

// Inicializar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📊 Dashboard disponível em: http://localhost:${PORT}`);
  console.log(`⏰ Coleta automática agendada para 07:00 diariamente`);
});

module.exports = app;
