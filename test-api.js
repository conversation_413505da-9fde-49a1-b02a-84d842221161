// Script simples para testar a API
const http = require('http');

function testAPI(endpoint, description) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: endpoint,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`✅ ${description}: OK`);
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   Dados: ${JSON.stringify(jsonData, null, 2).substring(0, 200)}...`);
          resolve(jsonData);
        } catch (error) {
          console.log(`❌ ${description}: Erro ao parsear JSON`);
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   Resposta: ${data.substring(0, 200)}...`);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${description}: ${error.message}`);
      reject(error);
    });

    req.setTimeout(5000, () => {
      console.log(`⏰ ${description}: Timeout`);
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testando API do AgroTech Dashboard...\n');

  const tests = [
    { endpoint: '/api/dashboard', description: 'Dashboard principal' },
    { endpoint: '/api/dashboard/resumo', description: 'Resumo do dashboard' },
    { endpoint: '/api/dashboard/status', description: 'Status da aplicação' },
    { endpoint: '/api/boi', description: 'Histórico do boi' },
    { endpoint: '/api/boi/atual', description: 'Preço atual do boi' },
    { endpoint: '/api/graos?tipo=soja', description: 'Histórico da soja' },
    { endpoint: '/api/graos?tipo=milho', description: 'Histórico do milho' },
    { endpoint: '/api/graos/atual?tipo=soja', description: 'Preço atual da soja' },
    { endpoint: '/api/graos/atual?tipo=milho', description: 'Preço atual do milho' }
  ];

  let sucessos = 0;
  let falhas = 0;

  for (const test of tests) {
    try {
      await testAPI(test.endpoint, test.description);
      sucessos++;
    } catch (error) {
      falhas++;
    }
    console.log(''); // Linha em branco
  }

  console.log('📊 Resultado dos testes:');
  console.log(`   ✅ Sucessos: ${sucessos}`);
  console.log(`   ❌ Falhas: ${falhas}`);
  console.log(`   📈 Taxa de sucesso: ${((sucessos / tests.length) * 100).toFixed(1)}%`);

  if (falhas === 0) {
    console.log('\n🎉 Todos os testes passaram! A API está funcionando corretamente.');
  } else {
    console.log('\n⚠️ Alguns testes falharam. Verifique os logs acima.');
  }
}

// Executar testes
runTests().catch(console.error);
