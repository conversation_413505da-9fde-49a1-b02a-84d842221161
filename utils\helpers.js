// Funções utilitárias para AgroTech Dashboard

/**
 * Formatar data para o padrão brasileiro
 * @param {string|Date} date - Data para formatar
 * @returns {string} Data formatada (DD/MM/AAAA)
 */
function formatDateBR(date) {
  const d = new Date(date);
  if (isNaN(d.getTime())) {
    return 'Data inválida';
  }
  
  return d.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Formatar data para ISO (YYYY-MM-DD)
 * @param {Date} date - Data para formatar
 * @returns {string} Data no formato ISO
 */
function formatDateISO(date = new Date()) {
  return date.toISOString().split('T')[0];
}

/**
 * Formatar valor monetário
 * @param {number} value - Valor para formatar
 * @param {string} currency - Moeda (padrão: BRL)
 * @returns {string} Valor formatado
 */
function formatCurrency(value, currency = 'BRL') {
  if (typeof value !== 'number' || isNaN(value)) {
    return 'R$ 0,00';
  }

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency
  }).format(value);
}

/**
 * Validar se uma string é um número válido
 * @param {string} str - String para validar
 * @returns {boolean} True se for um número válido
 */
function isValidNumber(str) {
  return !isNaN(parseFloat(str)) && isFinite(str);
}

/**
 * Extrair número de uma string (remove caracteres não numéricos)
 * @param {string} str - String contendo número
 * @returns {number|null} Número extraído ou null se inválido
 */
function extractNumber(str) {
  if (typeof str !== 'string') return null;
  
  // Remove tudo exceto números, vírgulas e pontos
  const cleaned = str.replace(/[^\d,.-]/g, '');
  
  // Substitui vírgula por ponto (padrão brasileiro)
  const normalized = cleaned.replace(',', '.');
  
  const number = parseFloat(normalized);
  return isNaN(number) ? null : number;
}

/**
 * Calcular variação percentual entre dois valores
 * @param {number} oldValue - Valor anterior
 * @param {number} newValue - Valor atual
 * @returns {number} Variação percentual
 */
function calculatePercentageChange(oldValue, newValue) {
  if (!oldValue || oldValue === 0) return 0;
  return ((newValue - oldValue) / oldValue) * 100;
}

/**
 * Gerar dados simulados para desenvolvimento
 * @param {number} days - Número de dias para gerar
 * @param {number} basePrice - Preço base
 * @param {number} volatility - Volatilidade (0-1)
 * @returns {Array} Array de dados simulados
 */
function generateMockData(days = 30, basePrice = 100, volatility = 0.1) {
  const data = [];
  let currentPrice = basePrice;
  
  for (let i = days; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Simular variação de preço
    const change = (Math.random() - 0.5) * 2 * volatility * basePrice;
    currentPrice = Math.max(currentPrice + change, basePrice * 0.5);
    
    data.push({
      data: formatDateISO(date),
      valor: Math.round(currentPrice * 100) / 100
    });
  }
  
  return data;
}

/**
 * Validar estrutura de dados de commodity
 * @param {Object} data - Dados para validar
 * @returns {boolean} True se válido
 */
function validateCommodityData(data) {
  if (!data || typeof data !== 'object') return false;
  
  const requiredFields = ['data', 'valor'];
  return requiredFields.every(field => data.hasOwnProperty(field));
}

/**
 * Sanitizar string para evitar XSS
 * @param {string} str - String para sanitizar
 * @returns {string} String sanitizada
 */
function sanitizeString(str) {
  if (typeof str !== 'string') return '';
  
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Debounce function para limitar execução
 * @param {Function} func - Função para debounce
 * @param {number} wait - Tempo de espera em ms
 * @returns {Function} Função com debounce
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Retry function com backoff exponencial
 * @param {Function} fn - Função para executar
 * @param {number} retries - Número de tentativas
 * @param {number} delay - Delay inicial em ms
 * @returns {Promise} Promise da execução
 */
async function retryWithBackoff(fn, retries = 3, delay = 1000) {
  try {
    return await fn();
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
      return retryWithBackoff(fn, retries - 1, delay * 2);
    }
    throw error;
  }
}

/**
 * Verificar se uma data está dentro de um range
 * @param {string|Date} date - Data para verificar
 * @param {number} days - Número de dias para trás
 * @returns {boolean} True se estiver no range
 */
function isDateInRange(date, days) {
  const checkDate = new Date(date);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return checkDate >= cutoffDate;
}

/**
 * Calcular estatísticas básicas de um array de valores
 * @param {Array} values - Array de números
 * @returns {Object} Estatísticas (min, max, avg, etc.)
 */
function calculateStats(values) {
  if (!Array.isArray(values) || values.length === 0) {
    return { min: 0, max: 0, avg: 0, count: 0 };
  }

  const numbers = values.filter(v => typeof v === 'number' && !isNaN(v));
  
  if (numbers.length === 0) {
    return { min: 0, max: 0, avg: 0, count: 0 };
  }

  const min = Math.min(...numbers);
  const max = Math.max(...numbers);
  const sum = numbers.reduce((acc, val) => acc + val, 0);
  const avg = sum / numbers.length;

  return {
    min: Math.round(min * 100) / 100,
    max: Math.round(max * 100) / 100,
    avg: Math.round(avg * 100) / 100,
    count: numbers.length
  };
}

/**
 * Converter timestamp para formato legível
 * @param {number} timestamp - Timestamp em ms
 * @returns {string} Data/hora formatada
 */
function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Verificar se o ambiente é de desenvolvimento
 * @returns {boolean} True se for desenvolvimento
 */
function isDevelopment() {
  return process.env.NODE_ENV === 'development';
}

/**
 * Verificar se o ambiente é de produção
 * @returns {boolean} True se for produção
 */
function isProduction() {
  return process.env.NODE_ENV === 'production';
}

module.exports = {
  formatDateBR,
  formatDateISO,
  formatCurrency,
  isValidNumber,
  extractNumber,
  calculatePercentageChange,
  generateMockData,
  validateCommodityData,
  sanitizeString,
  debounce,
  retryWithBackoff,
  isDateInRange,
  calculateStats,
  formatTimestamp,
  isDevelopment,
  isProduction
};
