// Sistema de logging para AgroTech Dashboard
const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = path.join(__dirname, '..', 'logs');
    this.ensureLogDirectory();
  }

  // Garantir que o diretório de logs existe
  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  // Formatar timestamp
  getTimestamp() {
    return new Date().toISOString();
  }

  // Formatar mensagem de log
  formatMessage(level, message, meta = {}) {
    const timestamp = this.getTimestamp();
    const metaStr = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  // Escrever log em arquivo
  writeToFile(filename, message) {
    const filePath = path.join(this.logDir, filename);
    const logMessage = message + '\n';
    
    try {
      fs.appendFileSync(filePath, logMessage);
    } catch (error) {
      console.error('Erro ao escrever log:', error);
    }
  }

  // Log de informação
  info(message, meta = {}) {
    const formattedMessage = this.formatMessage('info', message, meta);
    console.log(formattedMessage);
    this.writeToFile('combined.log', formattedMessage);
  }

  // Log de erro
  error(message, meta = {}) {
    const formattedMessage = this.formatMessage('error', message, meta);
    console.error(formattedMessage);
    this.writeToFile('error.log', formattedMessage);
    this.writeToFile('combined.log', formattedMessage);
  }

  // Log de warning
  warn(message, meta = {}) {
    const formattedMessage = this.formatMessage('warn', message, meta);
    console.warn(formattedMessage);
    this.writeToFile('combined.log', formattedMessage);
  }

  // Log de debug
  debug(message, meta = {}) {
    if (process.env.NODE_ENV === 'development') {
      const formattedMessage = this.formatMessage('debug', message, meta);
      console.log(formattedMessage);
      this.writeToFile('combined.log', formattedMessage);
    }
  }

  // Log específico para coleta de dados
  collector(message, meta = {}) {
    const formattedMessage = this.formatMessage('collector', message, meta);
    console.log(formattedMessage);
    this.writeToFile('collector.log', formattedMessage);
    this.writeToFile('combined.log', formattedMessage);
  }

  // Log de sucesso
  success(message, meta = {}) {
    const formattedMessage = this.formatMessage('success', message, meta);
    console.log(`✅ ${formattedMessage}`);
    this.writeToFile('combined.log', formattedMessage);
  }

  // Limpar logs antigos
  cleanOldLogs(daysToKeep = 30) {
    const files = ['combined.log', 'error.log', 'collector.log'];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    files.forEach(filename => {
      const filePath = path.join(this.logDir, filename);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          try {
            fs.unlinkSync(filePath);
            this.info(`Log antigo removido: ${filename}`);
          } catch (error) {
            this.error(`Erro ao remover log antigo: ${filename}`, { error: error.message });
          }
        }
      }
    });
  }

  // Obter estatísticas dos logs
  getLogStats() {
    const stats = {};
    const files = ['combined.log', 'error.log', 'collector.log'];

    files.forEach(filename => {
      const filePath = path.join(this.logDir, filename);
      
      if (fs.existsSync(filePath)) {
        const fileStats = fs.statSync(filePath);
        stats[filename] = {
          size: fileStats.size,
          modified: fileStats.mtime,
          lines: this.countLines(filePath)
        };
      } else {
        stats[filename] = null;
      }
    });

    return stats;
  }

  // Contar linhas em um arquivo
  countLines(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return content.split('\n').length - 1;
    } catch (error) {
      return 0;
    }
  }

  // Ler últimas linhas de um log
  getRecentLogs(filename = 'combined.log', lines = 100) {
    const filePath = path.join(this.logDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return [];
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const allLines = content.split('\n').filter(line => line.trim() !== '');
      return allLines.slice(-lines);
    } catch (error) {
      this.error('Erro ao ler logs recentes', { error: error.message });
      return [];
    }
  }

  // Buscar logs por padrão
  searchLogs(pattern, filename = 'combined.log') {
    const filePath = path.join(this.logDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return [];
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      const regex = new RegExp(pattern, 'i');
      
      return lines.filter(line => regex.test(line));
    } catch (error) {
      this.error('Erro ao buscar logs', { error: error.message, pattern });
      return [];
    }
  }
}

// Instância singleton do logger
const logger = new Logger();

module.exports = logger;
